const fetch = require("node-fetch");

const SOLVECAPTCHA_API_KEY = "66b4447f6efdc82957de2ae421659608";
const SITE_KEY = "0x4AAAAAABzweCbOQd_LmvY9";
const WEBSITE_URL = "https://blast.fun";

async function solveTurnstileCaptcha() {
  try {
    const submitData = {
      key: SOLVECAPTCHA_API_KEY,
      method: "turnstile",
      sitekey: SITE_KEY,
      pageurl: WEBSITE_URL,
    };

    const submitResponse = await fetch("https://api.solvecaptcha.com/in.php", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams(submitData),
    });

    const submitText = await submitResponse.text();

    if (submitText.startsWith("ERROR")) {
      throw new Error(`Failed to submit captcha: ${submitText}`);
    }

    if (!submitText.startsWith("OK|")) {
      throw new Error(`Unexpected submit response: ${submitText}`);
    }

    const captchaId = submitText.split("|")[1];

    await new Promise((resolve) => setTimeout(resolve, 20000));

    const maxAttempts = 24; // 2 minutes total (24 * 5 seconds)
    let attempts = 0;

    while (attempts < maxAttempts) {
      const resultResponse = await fetch(
        `https://api.solvecaptcha.com/res.php?key=${SOLVECAPTCHA_API_KEY}&action=get&id=${captchaId}`
      );

      const resultText = await resultResponse.text();
      console.log(`Attempt ${attempts + 1}: ${resultText}`);

      if (resultText === "CAPCHA_NOT_READY") {
        attempts++;
        if (attempts < maxAttempts) {
          console.log("Captcha not ready, waiting 5 seconds...");
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
        continue;
      }

      if (resultText.startsWith("ERROR")) {
        throw new Error(`Error getting result: ${resultText}`);
      }

      if (resultText.startsWith("OK|")) {
        const token = resultText.split("|")[1];
        console.log("✅ Turnstile captcha solved successfully!");
        return token;
      }

      throw new Error(`Unexpected result response: ${resultText}`);
    }

    throw new Error(
      `Timeout: Captcha not solved within ${maxAttempts * 5} seconds`
    );
  } catch (error) {
    console.error("❌ Error solving Turnstile captcha:", error.message);
    throw error;
  }
}

async function callBlastTokenProtectionAPI(turnstileToken) {
  try {
    console.log("🔐 Calling Blast.fun token protection signature API...");

    // Default request body parameters
    const requestBody = {
      poolId:
        "0x85c6ad9ad5eacb5625dafd21f3bb77308029555f3463cb2f1a2709cb3dc876e5",
      amount: "0.1",
      walletAddress:
        "0xd70f3dc3382cb81e0dce0d906a076e48df06f2ddefc4e7f78561ecffaf77c978",
      coinType:
        "0x4bc9887298fc1583e615b694cb625f2716a7a2ae60ca927387b6572a1d65d782::chop::CHOP",
      decimals: 9,
      turnstileToken: turnstileToken, // Use the provided captcha token
    };

    console.log("📤 Request body:", JSON.stringify(requestBody, null, 2));

    const response = await fetch(
      "https://blast.fun/api/token-protection/signature",
      {
        method: "POST",
        headers: {
          accept: "application/json, text/plain, */*",
          "accept-language": "en-US,en;q=0.9",
          "content-type": "application/json",
          priority: "u=1, i",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"macOS"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          Referer: "https://blast.fun/",
          "Referrer-Policy": "strict-origin-when-cross-origin",
        },
        body: JSON.stringify(requestBody),
      }
    );

    console.log("📡 Response status:", response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `API request failed with status ${response.status}: ${errorText}`
      );
    }

    const responseData = await response.json();
    console.log(
      "✅ API response received:",
      JSON.stringify(responseData, null, 2)
    );

    return responseData;
  } catch (error) {
    console.error("❌ Error calling Blast.fun API:", error.message);
    throw error;
  }
}

async function solveAndCallBlastAPI() {
  try {
    console.log("🔄 Starting complete workflow...\n");

    console.log("Step 1: Solving Turnstile captcha...");
    const turnstileToken = await solveTurnstileCaptcha(WEBSITE_URL, SITE_KEY);
    console.log(
      "✅ Captcha solved, token:",
      turnstileToken.substring(0, 20) + "...\n"
    );

    // Step 2: Call Blast.fun API with the token
    console.log("Step 2: Calling Blast.fun API...");
    const apiResponse = await callBlastTokenProtectionAPI(turnstileToken);

    console.log("🎉 Complete workflow finished successfully!");
    return apiResponse;
  } catch (error) {
    console.error("❌ Workflow failed:", error.message);
    throw error;
  }
}

async function examples() {
  try {
    const result1 = await solveAndCallBlastAPI();
    console.log("Final result:", result1);
  } catch (error) {
    console.error("❌ Example failed:", error.message);
  }
}

async function main() {
  console.log("🚀 Starting Turnstile captcha solver...");

  await examples();
}

main();
